import type { Metada<PERSON> } from "next";
import { Inter, Poppins } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { StructuredData, websiteStructuredData, organizationStructuredData } from "@/components/StructuredData";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
  preload: true,
  fallback: ['system-ui', 'arial'],
});

const poppins = Poppins({
  variable: "--font-poppins",
  weight: ["400", "500", "600", "700"],
  subsets: ["latin"],
  display: "swap",
  preload: true,
  fallback: ['system-ui', 'arial'],
});

const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://example.com';

export const metadata: Metadata = {
  title: {
    default: "Landing Template - Modern & Fast Landing Pages",
    template: "%s | Landing Template"
  },
  description: "Template landing page modern yang dibangun dengan Next.js 15 dan Tailwind CSS. Responsif, cepat, SEO-friendly, dan mudah dikustomisasi untuk bisnis Anda.",
  keywords: ["landing page", "next.js", "tailwind css", "template", "responsive", "modern", "fast", "SEO"],
  authors: [{ name: "Landing Template Team" }],
  creator: "Landing Template",
  publisher: "Landing Template",
  metadataBase: new URL(baseUrl),
  openGraph: {
    title: "Landing Template - Modern & Fast Landing Pages",
    description: "Template landing page modern yang dibangun dengan Next.js 15 dan Tailwind CSS. Responsif, cepat, SEO-friendly, dan mudah dikustomisasi untuk bisnis Anda.",
    url: '/',
    siteName: 'Landing Template',
    type: 'website',
    locale: 'id_ID',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Landing Template - Modern Landing Page',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: "Landing Template - Modern & Fast Landing Pages",
    description: "Template landing page modern yang dibangun dengan Next.js 15 dan Tailwind CSS. Responsif, cepat, SEO-friendly, dan mudah dikustomisasi untuk bisnis Anda.",
    images: ['/og-image.jpg'],
  },
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon.svg',
    apple: '/favicon.svg',
  },
  alternates: {
    canonical: '/',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        {/* Font preloading for better performance */}
        <link
          rel="preconnect"
          href="https://fonts.googleapis.com"
        />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />

        {/* Structured Data for SEO */}
        <StructuredData data={websiteStructuredData} />
        <StructuredData data={organizationStructuredData} />

        {/* Theme script - optimized */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function(){
                try {
                  var s = localStorage.getItem('theme');
                  var m = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
                  var t = s || m;
                  var r = document.documentElement;
                  r.classList.remove('light', 'dark');
                  r.classList.add(t);
                } catch(e){}
              })();
            `,
          }}
        />
      </head>
      <body className={`${inter.variable} ${poppins.variable} antialiased`}>
        <ThemeProvider>
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
